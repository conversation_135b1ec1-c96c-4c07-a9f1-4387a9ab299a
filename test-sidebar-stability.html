<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sidebar Stability Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Sidebar Stability Test</h1>
        <p>This test verifies that the sidebar remains stable during chart loading operations.</p>
        
        <div class="test-section">
            <h3>Test 1: Sidebar Toggle During Chart Loading</h3>
            <p>This test simulates heavy chart loading and attempts to toggle the sidebar.</p>
            <button class="test-button" onclick="runSidebarToggleTest()">Run Test</button>
            <div id="test1-status" class="status info">Ready to run test</div>
        </div>
        
        <div class="test-section">
            <h3>Test 2: Scroll Performance During Chart Loading</h3>
            <p>This test simulates scroll events during chart loading to check performance.</p>
            <button class="test-button" onclick="runScrollPerformanceTest()">Run Test</button>
            <div id="test2-status" class="status info">Ready to run test</div>
        </div>
        
        <div class="test-section">
            <h3>Test 3: Multiple Chart Loading Simulation</h3>
            <p>This test simulates multiple charts loading simultaneously.</p>
            <button class="test-button" onclick="runMultipleChartTest()">Run Test</button>
            <div id="test3-status" class="status info">Ready to run test</div>
        </div>
        
        <div class="test-section">
            <h3>Test Log</h3>
            <div id="test-log" class="log">Test log will appear here...\n</div>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <script>
        // Mock ViewportLazyLoader for testing
        window.ViewportLazyLoader = {
            loadingComponents: new Set(),
            protectSidebarDuringLoading: function(enable) {
                const sidebar = document.querySelector('.sidebar');
                if (sidebar) {
                    if (enable) {
                        sidebar.dataset.loadingProtection = 'true';
                        log('🛡️ Sidebar protection enabled');
                    } else {
                        if (this.loadingComponents.size === 0) {
                            delete sidebar.dataset.loadingProtection;
                            log('🛡️ Sidebar protection disabled');
                        }
                    }
                }
            }
        };

        function log(message) {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('test-log').textContent = 'Test log cleared...\n';
        }

        function setTestStatus(testId, status, message) {
            const element = document.getElementById(`test${testId}-status`);
            element.className = `status ${status}`;
            element.textContent = message;
        }

        async function runSidebarToggleTest() {
            log('Starting Sidebar Toggle Test...');
            setTestStatus(1, 'info', 'Running test...');
            
            try {
                // Simulate chart loading
                window.ViewportLazyLoader.loadingComponents.add('test-chart-1');
                window.ViewportLazyLoader.protectSidebarDuringLoading(true);
                log('📊 Simulated chart loading started');
                
                // Try to toggle sidebar (should be blocked)
                const sidebar = document.querySelector('.sidebar');
                if (sidebar && sidebar.dataset.loadingProtection === 'true') {
                    log('✅ Sidebar protection is active - toggle would be blocked');
                    setTestStatus(1, 'success', 'Test passed: Sidebar protected during loading');
                } else {
                    log('❌ Sidebar protection not working');
                    setTestStatus(1, 'error', 'Test failed: Sidebar not protected');
                    return;
                }
                
                // Simulate loading completion
                await new Promise(resolve => setTimeout(resolve, 1000));
                window.ViewportLazyLoader.loadingComponents.delete('test-chart-1');
                window.ViewportLazyLoader.protectSidebarDuringLoading(false);
                log('📊 Simulated chart loading completed');
                
                if (!sidebar.dataset.loadingProtection) {
                    log('✅ Sidebar protection removed after loading');
                    setTestStatus(1, 'success', 'Test passed: Protection properly removed');
                } else {
                    log('❌ Sidebar protection not removed');
                    setTestStatus(1, 'error', 'Test failed: Protection not removed');
                }
                
            } catch (error) {
                log(`❌ Test error: ${error.message}`);
                setTestStatus(1, 'error', `Test failed: ${error.message}`);
            }
        }

        async function runScrollPerformanceTest() {
            log('Starting Scroll Performance Test...');
            setTestStatus(2, 'info', 'Running test...');
            
            try {
                // Simulate chart loading
                window.ViewportLazyLoader.loadingComponents.add('test-chart-2');
                log('📊 Simulated chart loading for scroll test');
                
                // Measure scroll handling performance
                const startTime = performance.now();
                
                // Simulate multiple scroll events
                for (let i = 0; i < 100; i++) {
                    const isChartLoading = window.ViewportLazyLoader.loadingComponents.size > 0;
                    const scrollDelay = isChartLoading ? 32 : 16;
                    
                    if (scrollDelay === 32) {
                        log(`✅ Scroll delay increased during loading (${scrollDelay}ms)`);
                        break;
                    }
                }
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                log(`📈 Scroll performance test completed in ${duration.toFixed(2)}ms`);
                
                // Clean up
                window.ViewportLazyLoader.loadingComponents.delete('test-chart-2');
                
                setTestStatus(2, 'success', `Test passed: Scroll handling optimized during loading`);
                
            } catch (error) {
                log(`❌ Test error: ${error.message}`);
                setTestStatus(2, 'error', `Test failed: ${error.message}`);
            }
        }

        async function runMultipleChartTest() {
            log('Starting Multiple Chart Loading Test...');
            setTestStatus(3, 'info', 'Running test...');
            
            try {
                // Simulate multiple charts loading
                const chartIds = ['chart-1', 'chart-2', 'chart-3', 'chart-4'];
                
                chartIds.forEach(id => {
                    window.ViewportLazyLoader.loadingComponents.add(id);
                    log(`📊 Started loading ${id}`);
                });
                
                window.ViewportLazyLoader.protectSidebarDuringLoading(true);
                log(`🛡️ Sidebar protection enabled for ${chartIds.length} charts`);
                
                // Simulate charts completing one by one
                for (let i = 0; i < chartIds.length; i++) {
                    await new Promise(resolve => setTimeout(resolve, 500));
                    const chartId = chartIds[i];
                    window.ViewportLazyLoader.loadingComponents.delete(chartId);
                    log(`✅ Completed loading ${chartId}`);
                    
                    // Try to disable protection (should only work when all are done)
                    window.ViewportLazyLoader.protectSidebarDuringLoading(false);
                    
                    const sidebar = document.querySelector('.sidebar');
                    if (i < chartIds.length - 1) {
                        // Should still be protected
                        if (sidebar && sidebar.dataset.loadingProtection === 'true') {
                            log(`✅ Protection maintained with ${chartIds.length - i - 1} charts remaining`);
                        } else {
                            log(`❌ Protection removed too early`);
                            setTestStatus(3, 'error', 'Test failed: Protection removed too early');
                            return;
                        }
                    } else {
                        // Should be removed now
                        if (sidebar && !sidebar.dataset.loadingProtection) {
                            log(`✅ Protection removed after all charts completed`);
                        } else {
                            log(`❌ Protection not removed after completion`);
                            setTestStatus(3, 'error', 'Test failed: Protection not removed');
                            return;
                        }
                    }
                }
                
                setTestStatus(3, 'success', 'Test passed: Multiple chart loading handled correctly');
                
            } catch (error) {
                log(`❌ Test error: ${error.message}`);
                setTestStatus(3, 'error', `Test failed: ${error.message}`);
            }
        }

        // Initialize test environment
        document.addEventListener('DOMContentLoaded', function() {
            log('Test environment initialized');
            
            // Create a mock sidebar element for testing
            const sidebar = document.createElement('div');
            sidebar.className = 'sidebar';
            sidebar.style.display = 'none'; // Hidden for testing
            document.body.appendChild(sidebar);
            
            log('Mock sidebar element created for testing');
        });
    </script>
</body>
</html>
